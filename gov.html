<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sri Lanka Government Mind Map</title>
    <script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const App = () => {
          const [expandedNodes, setExpandedNodes] = React.useState({
            government: true // Start with government node expanded
          });
          
          // Data extracted from the gazette document
          const ministersData = [
            {
              id: "defence",
              name: "Minister of Defence",
              color: "#8dd3c7",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "Sri Lanka Army", 
                    "Sri Lanka Navy", 
                    "Sri Lanka Air Force",
                    "Civil Security Department",
                    "Department of Coast Guard",
                    "Office of the Chief of Defence Staff",
                    "Defence Services Command and Staff College"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Implementation of the Public Security Ordinance",
                    "Prevention of terrorism",
                    "Defence of Sri Lanka",
                    "Formulation and implementation of policies on national security"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Army Act (Chapter 357)",
                    "Navy Act (Chapter 358)",
                    "Air Force Act (Chapter 359)",
                    "Public Security Ordinance (Chapter 40)",
                    "Prevention of Terrorism Act, No. 48 of 1979",
                    "Civil Security Department Act, No. 35 of 2007",
                    "Department of Coast Guard Act, No. 41 of 2009",
                    "Official Secrets Act (Chapter 42)"
                  ]
                }
              ]
            },
            {
              id: "finance",
              name: "Minister of Finance, Planning and Economic Development",
              color: "#80b1d3",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "General Treasury",
                    "Department of National Planning",
                    "Department of External Resources",
                    "Department of Fiscal Policy",
                    "Department of National Budget",
                    "Department of Treasury Operations",
                    "Department of Public Enterprises",
                    "Department of Management Services",
                    "Department of Public Finance",
                    "Department of State Accounts",
                    "Department of Inland Revenue",
                    "Sri Lanka Customs",
                    "Department of Import and Export Control",
                    "Department of Census and Statistics",
                    "Department of Valuation",
                    "Department of Public Debt",
                    "Department of Trade and Investment Policy"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Formulation of national economic and financial policies and strategies",
                    "Preparation of National Development and Public Investment Programmes",
                    "Formulation of fiscal and macro-fiscal management policies",
                    "Implementation of appropriate policy measures to ensure sustainable economic growth and stability",
                    "Execution of appropriate measures to ensure debt sustainability",
                    "Management of the Consolidated Fund",
                    "Overall supervision of revenue agencies",
                    "Provision of direction and guidance to State Banks and Financial Agencies",
                    "Public expenditure management",
                    "Promotion of economic potential in Sri Lanka and foreign direct investment"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Appropriation Acts",
                    "Customs Ordinance, No. 17 of 1956",
                    "Foreign Loans Act No. 29 of 1957",
                    "Public Financial Management Act No. 44 of 2024",
                    "Regulation of Insurance Industry Act No. 43 of 2000",
                    "Value Added Tax Act No. 14 of 2002",
                    "Finance Act, No. 38 of 1971",
                    "Inland Revenue Act No. 24 of 2017",
                    "Public Debt Management Act No. 33 of 2024",
                    "Economic Transformation Act No. 45 of 2024",
                    "Colombo Port City Economic Commission Act No. 11 of 2021"
                  ]
                }
              ]
            },
            {
              id: "energy",
              name: "Minister of Energy",
              color: "#fdb462",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "Ceylon Electricity Board and its subsidiary Companies",
                    "Ceylon Electricity Company",
                    "Lanka Coal Company (Pvt) Ltd",
                    "LTL Holdings (Pvt.) Ltd.",
                    "Ceylon Petroleum Corporation",
                    "Ceylon Petroleum Storage Terminal Ltd.",
                    "Petroleum Development Authority of Sri Lanka",
                    "Polipto Lanka (Pvt) Ltd",
                    "Sri Lanka Sustainable Energy Authority",
                    "Sri Lanka Atomic Energy Board",
                    "Sri Lanka Atomic Energy Regulatory Council",
                    "Trinco Petroleum Terminal (Pvt) Ltd"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Formulation and implementation of policies related to energy sector",
                    "Generation, transmission and distribution of electricity",
                    "Development of renewable energy sources",
                    "Import, refining, storage, distribution and marketing of petroleum products",
                    "Regulation of the petroleum industry",
                    "Promotion of sustainable energy solutions"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Ceylon Electricity Board Act No. 17 of 1969",
                    "Sri Lanka Electricity Act",
                    "Ceylon Petroleum Corporation Act",
                    "Sri Lanka Sustainable Energy Authority Act",
                    "Atomic Energy Authority Act"
                  ]
                }
              ]
            },
            {
              id: "justice",
              name: "Minister of Justice and National Integration",
              color: "#b3de69",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "Attorney General's Department",
                    "Legal Draftsman's Department",
                    "Department of Debt Conciliation Board",
                    "Department of Government Analyst",
                    "Office of the Registrar of the Supreme Court",
                    "Law Commission of Sri Lanka",
                    "Superior Courts Complex Board of Management",
                    "Legal Aid Commission of Sri Lanka",
                    "Mediation Boards Commission",
                    "Council of Legal Education",
                    "Commercial Mediation Centre of Sri Lanka",
                    "Department of Prisons",
                    "Community Based Correction Department",
                    "Training Schools for Youthful Offenders",
                    "Rehabilitation Commissioner General's Office",
                    "Department of Official Languages",
                    "Official Languages Commission",
                    "Office for National Unity and Reconciliation",
                    "Office on Missing Persons",
                    "Office for Reparations"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Formulation and implementation of policies related to justice and national integration",
                    "Making necessary reforms to the legal system",
                    "Re-documentation and consolidation of laws",
                    "Administration of courts of justice",
                    "Prevention of law's delays in the courts system",
                    "Criminal prosecutions and civil proceedings on behalf of the government",
                    "Providing legal advice to the government",
                    "Drafting of legislation",
                    "Making recommendations for pardons and commutations"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Superior Courts Complex Board of Management Act No. 50 of 1987",
                    "Judicature Act No. 2 of 1978",
                    "Law Commission Act No. 3 of 1969",
                    "Legal Aid Law No. 11 of 1978",
                    "Mediation Boards Act No. 72 of 1988",
                    "Assistance to and Protection of Victims of Crime and Witnesses Act No. 4 of 2015",
                    "Prisons Ordinance No. 16 of 1877",
                    "Community Based Corrections Act No. 46 of 1999",
                    "Official Languages Act No. 33 of 1956",
                    "Office for National Unity and Reconciliation Act No. 1 of 2024"
                  ]
                }
              ]
            },
            {
              id: "public_security",
              name: "Minister of Public Security and Parliamentary Affairs",
              color: "#d9d9d9",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "Sri Lanka Police",
                    "Department of Immigration and Emigration",
                    "Department of Registration of Persons",
                    "National Dangerous Drugs Control Board",
                    "National Authority for the Implementation of Chemical Weapons Convention"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Maintenance of law and order",
                    "Implementation of strategies for community discipline",
                    "Prevention and combating of crimes and anti-social activities",
                    "Controlling of vehicular traffic",
                    "Implementing reforms to police services",
                    "Coordinating affairs of non-governmental organisations",
                    "Matters relating to Immigration and Emigration",
                    "Prevention and control of dangerous drugs",
                    "Matters relating to Parliament and Members of Parliament"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Police Ordinance",
                    "Immigrants and Emigrants Act",
                    "Registration of Persons Act",
                    "National Dangerous Drugs Control Board Act No. 11 of 1984",
                    "Drug Dependent Persons (Treatment and Rehabilitation) Act, No. 54 of 2007",
                    "Conventions against Illicit Traffic in Narcotic Drugs and Psychotropic Substances Act No. 1 of 2008",
                    "Online Safety Act No. 9 of 2024"
                  ]
                }
              ]
            },
            {
              id: "foreign",
              name: "Minister of Foreign Affairs, Foreign Employment and Tourism",
              color: "#bc80bd",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "Diplomatic Missions in abroad",
                    "National Oceanic Affairs Committee Secretariat",
                    "Lakshman Kadirgamar Institute for International Relations and Strategic Studies",
                    "Sri Lanka Foreign Employment Bureau",
                    "Foreign Employment Agency of Sri Lanka (Pvt.) Ltd",
                    "Sri Lanka Tourism Promotion Bureau",
                    "Sri Lanka Tourism Development Authority"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Foreign relations and diplomatic affairs",
                    "Protection of Sri Lanka's interests abroad",
                    "Promotion of international cooperation",
                    "Consular functions",
                    "Regulation and promotion of foreign employment",
                    "Development and promotion of tourism"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Diplomatic Privileges Act",
                    "Consular Functions Act",
                    "Lakshman Kadirgamar Institute for International Relations and Strategic Studies Act",
                    "Sri Lanka Bureau of Foreign Employment Act",
                    "Tourism Act"
                  ]
                }
              ]
            },
            {
              id: "labour",
              name: "Minister of Labour",
              color: "#ccebc5",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "Department of Labour",
                    "National Institute of Labour Studies",
                    "National Institute for Occupational Safety and Health",
                    "Office of the Commissioner of Workmen's Compensation",
                    "Shrama Vasana Fund",
                    "Department of Manpower and Employment"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Formulation and implementation of policies for labour relations",
                    "Employee administration, welfare and health",
                    "Ensuring compliance with international labour standards",
                    "Regulation of employment conditions",
                    "Occupational safety and health"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Employees' Councils Act No. 32 of 1979",
                    "Employees' Provident Fund Act No. 15 of 1958",
                    "Employment of Women, Young Persons, and Children Act No. 47 of 1956",
                    "Factories Ordinance No. 45 of 1942",
                    "Industrial Disputes Act No. 43 of 1950",
                    "Maternity Benefits Ordinance No. 32 of 1939",
                    "National Institute of Occupational Safety and Health Act No. 38 of 2009",
                    "Payment of Gratuity Act No. 12 of 1983",
                    "Shop and Office Employees (Regulation of Employment and Remuneration) Act"
                  ]
                }
              ]
            },
            {
              id: "health",
              name: "Minister of Health",
              color: "#ffed6f",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "Department of Health Services",
                    "Medical Research Institute",
                    "State Pharmaceuticals Corporation",
                    "National Medicines Regulatory Authority",
                    "Sri Lanka Medical Council"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Healthcare services",
                    "Disease prevention and control",
                    "Medical education and training",
                    "Regulation of pharmaceuticals",
                    "Hospital administration"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Medical Ordinance",
                    "Health Services Act",
                    "National Medicines Regulatory Authority Act",
                    "Quarantine and Prevention of Diseases Ordinance"
                  ]
                }
              ]
            }
          ];

          const toggleNode = (id) => {
            setExpandedNodes(prev => ({
              ...prev,
              [id]: !prev[id]
            }));
          };

          const renderCategoryNode = (minister, category, catIndex) => {
            const categoryId = `${minister.id}-${category.type}`;
            const isExpanded = expandedNodes[categoryId];
            
            return (
              <div 
                key={categoryId} 
                className="category-container"
                style={{
                  transform: `rotate(${(catIndex - 1) * 25}deg) translateX(50px)`,
                  transformOrigin: "0 50%"
                }}
              >
                <div 
                  className="category-header" 
                  style={{ backgroundColor: category.color }}
                  onClick={() => toggleNode(categoryId)}
                >
                  <span className="expand-icon">
                    {isExpanded ? '▼' : '►'}
                  </span>
                  <h4>{category.name}</h4>
                </div>
                
                {isExpanded && (
                  <ul className="items-list">
                    {category.items.map((item, itemIndex) => (
                      <li 
                        key={`${categoryId}-${itemIndex}`}
                        style={{
                          animationDelay: `${itemIndex * 0.05}s`
                        }}
                      >
                        {item}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            );
          };

          const renderMinisterNode = (minister, index, totalMinisters) => {
            const isExpanded = expandedNodes[minister.id];
            const angle = (index * (360 / totalMinisters));
            const radians = angle * (Math.PI / 180);
            const x = Math.cos(radians) * 250;
            const y = Math.sin(radians) * 250;
            
            return (
              <div 
                key={minister.id} 
                className="minister-node"
                style={{
                  position: 'absolute',
                  left: `calc(50% + ${x}px)`,
                  top: `calc(50% + ${y}px)`,
                  transform: 'translate(-50%, -50%)',
                  transformOrigin: 'center',
                  transition: 'all 0.5s ease-out',
                  zIndex: isExpanded ? 10 : 1
                }}
              >
                <div className="branch-line" style={{
                  position: 'absolute',
                  width: '250px',
                  height: '2px',
                  background: `linear-gradient(to right, #4a4a4a, ${minister.color})`,
                  top: '50%',
                  right: '100%',
                  transformOrigin: 'right center',
                  transform: `rotate(${180 + angle}deg)`,
                  zIndex: -1
                }}></div>
                
                <div 
                  className="minister-header" 
                  style={{ backgroundColor: minister.color }}
                  onClick={() => toggleNode(minister.id)}
                >
                  <span className="expand-icon">{isExpanded ? '▼' : '►'}</span>
                  <h3>{minister.name}</h3>
                </div>
                
                {isExpanded && (
                  <div className="minister-details">
                    {minister.children.map((category, catIndex) => 
                      renderCategoryNode(minister, category, catIndex)
                    )}
                  </div>
                )}
              </div>
            );
          };

          return (
            <div className="government-mind-map">
              <div className="central-node">
                <div 
                  className="government-header"
                  onClick={() => toggleNode('government')}
                >
                  <h2>Government of Sri Lanka</h2>
                  <div className="emblem"></div>
                </div>
              </div>
              
              {expandedNodes.government && (
                <div className="ministers-container">
                  {ministersData.map((minister, index) => 
                    renderMinisterNode(minister, index, ministersData.length)
                  )}
                </div>
              )}
              
              <div className="legend">
                <h3>Color Legend</h3>
                <div className="legend-item">
                  <span className="color-box" style={{backgroundColor: "#ffffb3"}}></span>
                  <span>Departments & Statutory Institutions</span>
                </div>
                <div className="legend-item">
                  <span className="color-box" style={{backgroundColor: "#bebada"}}></span>
                  <span>Subjects & Functions</span>
                </div>
                <div className="legend-item">
                  <span className="color-box" style={{backgroundColor: "#fb8072"}}></span>
                  <span>Laws & Acts</span>
                </div>
              </div>
              
              <style>{`
                body {
                  margin: 0;
                  padding: 0;
                  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                  font-family: 'Poppins', sans-serif;
                  min-height: 100vh;
                  overflow-x: hidden;
                }
                
                .government-mind-map {
                  position: relative;
                  width: 100%;
                  height: 100vh;
                  overflow: auto;
                }
                
                .central-node {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  z-index: 100;
                }
                
                .government-header {
                  background: linear-gradient(135deg, #4a00e0, #8e2de2);
                  color: white;
                  padding: 15px 25px;
                  border-radius: 50%;
                  width: 150px;
                  height: 150px;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  text-align: center;
                  cursor: pointer;
                  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
                  transition: all 0.3s ease;
                }
                
                .government-header:hover {
                  transform: scale(1.05);
                  box-shadow: 0 15px 30px rgba(0,0,0,0.3);
                }
                
                .government-header h2 {
                  margin: 0;
                  font-size: 1.2rem;
                  font-weight: 600;
                }
                
                .emblem {
                  width: 50px;
                  height: 50px;
                  background-image: url('https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Emblem_of_Sri_Lanka.svg/800px-Emblem_of_Sri_Lanka.svg.png');
                  background-size: contain;
                  background-position: center;
                  background-repeat: no-repeat;
                  margin-top: 10px;
                }
                
                .ministers-container {
                  position: relative;
                  width: 100%;
                  height: 100%;
                }
                
                .minister-node {
                  width: 220px;
                  animation: fadeIn 0.5s ease-out forwards;
                }
                
                .minister-header {
                  padding: 12px 15px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  border-radius: 30px;
                  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                  transition: all 0.3s ease;
                  position: relative;
                  z-index: 2;
                }
                
                .minister-header:hover {
                  transform: translateY(-3px);
                  box-shadow: 0 6px 12px rgba(0,0,0,0.15);
                }
                
                .minister-header h3 {
                  margin: 0;
                  color: #333;
                  font-size: 0.9rem;
                  font-weight: 500;
                }
                
                .minister-details {
                  padding: 15px;
                  background-color: rgba(255, 255, 255, 0.9);
                  border-radius: 15px;
                  margin-top: 10px;
                  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                  position: relative;
                  z-index: 1;
                  animation: slideDown 0.3s ease-out forwards;
                }
                
                .category-container {
                  margin-bottom: 12px;
                  border-radius: 10px;
                  overflow: hidden;
                  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
                  transition: all 0.5s ease;
                  position: relative;
                }
                
                .category-header {
                  padding: 8px 12px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  border-radius: 8px;
                  transition: all 0.3s ease;
                }
                
                .category-header:hover {
                  filter: brightness(1.05);
                }
                
                .category-header h4 {
                  margin: 0;
                  color: #333;
                  font-size: 0.8rem;
                  font-weight: 500;
                }
                
                .expand-icon {
                  margin-right: 10px;
                  font-size: 12px;
                  color: #333;
                }
                
                .items-list {
                  margin: 0;
                  padding: 10px 10px 10px 30px;
                  background-color: white;
                  border-radius: 0 0 8px 8px;
                  max-height: 200px;
                  overflow-y: auto;
                }
                
                .items-list li {
                  margin-bottom: 5px;
                  font-size: 0.8rem;
                  opacity: 0;
                  animation: fadeIn 0.5s ease-out forwards;
                }
                
                .legend {
                  position: fixed;
                  bottom: 20px;
                  right: 20px;
                  padding: 15px;
                  border-radius: 10px;
                  background-color: rgba(255, 255, 255, 0.9);
                  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                  z-index: 1000;
                }
                
                .legend h3 {
                  margin-top: 0;
                  margin-bottom: 10px;
                  color: #333;
                  font-size: 0.9rem;
                }
                
                .legend-item {
                  display: flex;
                  align-items: center;
                  margin-bottom: 8px;
                }
                
                .legend-item span {
                  font-size: 0.8rem;
                }
                
                .color-box {
                  display: inline-block;
                  width: 20px;
                  height: 20px;
                  margin-right: 10px;
                  border-radius: 3px;
                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                
                @keyframes fadeIn {
                  from { opacity: 0; }
                  to { opacity: 1; }
                }
                
                @keyframes slideDown {
                  from { 
                    opacity: 0;
                    transform: translateY(-10px);
                  }
                  to { 
                    opacity: 1;
                    transform: translateY(0);
                  }
                }
                
                /* For better mobile experience */
                @media (max-width: 1200px) {
                  .government-mind-map {
                    height: auto;
                    min-height: 100vh;
                    padding: 20px;
                  }
                  
                  .central-node {
                    position: relative;
                    top: 0;
                    left: 0;
                    transform: none;
                    margin: 20px auto;
                  }
                  
                  .ministers-container {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 20px;
                    margin-top: 30px;
                  }
                  
                  .minister-node {
                    position: relative !important;
                    top: auto !important;
                    left: auto !important;
                    transform: none !important;
                    width: 100%;
                    max-width: 400px;
                  }
                  
                  .branch-line {
                    display: none;
                  }
                  
                  .category-container {
                    transform: none !important;
                  }
                  
                  .legend {
                    position: relative;
                    margin-top: 30px;
                    right: auto;
                    bottom: auto;
                  }
                }
              `}</style>
            </div>
          );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>