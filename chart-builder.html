<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Builder</title>
    <script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        gray: {
                            750: '#2b3544',
                            850: '#1e2633',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideIn: {
                            '0%': { transform: 'translateX(100%)' },
                            '100%': { transform: 'translateX(0)' },
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50 dark:bg-gray-900 font-sans">
    <div id="root"></div>
    
    <script type="text/babel">
        const App = () => {
            const [isAIPanelOpen, setIsAIPanelOpen] = React.useState(true);
            const [isDarkMode, setIsDarkMode] = React.useState(false);
            const [showGoogleSheetsModal, setShowGoogleSheetsModal] = React.useState(false);
            const [promptHistory, setPromptHistory] = React.useState([
                { id: 1, prompt: "Generate a bar chart showing sales data", response: "Created bar chart with monthly sales figures", timestamp: "2 mins ago" },
                { id: 2, prompt: "Add revenue forecast line", response: "Added forecast line to existing chart", timestamp: "5 mins ago" }
            ]);

            const toggleDarkMode = () => {
                setIsDarkMode(!isDarkMode);
                document.documentElement.classList.toggle('dark');
            };

            return (                <div className="flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
                    {/* Main Content Area */}
                    <main className="flex-1 overflow-auto">
                        {/* Top Bar */}
                        <div className="sticky top-0 z-10 backdrop-blur-md bg-gray-50/80 dark:bg-gray-900/80 border-b border-gray-200 dark:border-gray-800">
                            <div className="flex justify-between items-center px-6 py-4">
                                <h1 className="text-2xl font-semibold text-gray-800 dark:text-white tracking-tight">
                                    Chart Builder
                                </h1>
                                <div className="flex items-center gap-4">
                                    <button 
                                        onClick={() => setShowGoogleSheetsModal(true)}
                                        className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg flex items-center gap-2 transition-colors shadow-sm shadow-primary-500/20 hover:shadow-primary-500/30"
                                    >
                                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                        </svg>
                                        <span>Import from Google Sheets</span>
                                    </button>
                                    <button 
                                        onClick={toggleDarkMode}
                                        className="p-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                                    >
                                        {isDarkMode ? '☀️' : '🌙'}
                                    </button>
                                </div>
                            </div>
                        </div>                        {/* Chart Dashboard */}
                        <div className="p-6">
                            <div className="chart-dashboard grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {/* Sample Chart Card */}
                                <div className="chart-card group bg-white dark:bg-gray-800 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 dark:border-gray-750">
                                    <div className="flex justify-between items-center p-4 border-b border-gray-100 dark:border-gray-750">
                                        <h3 className="text-lg font-medium text-gray-800 dark:text-white">Monthly Sales</h3>
                                        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                            <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-750 rounded-lg transition-colors">
                                                <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                </svg>
                                            </button>
                                            <button className="p-2 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors">
                                                <svg className="w-4 h-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div className="p-4">
                                        <div className="chart-area h-64 bg-gray-50 dark:bg-gray-850 rounded-xl border border-gray-100 dark:border-gray-750 flex items-center justify-center">
                                            <span className="text-gray-400 dark:text-gray-500">Chart preview</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>                    {/* AI Copilot Panel */}
                    <aside className={`ai-section fixed right-0 top-0 h-full w-96 bg-white dark:bg-gray-800 shadow-2xl transform transition-all duration-300 border-l border-gray-200 dark:border-gray-750 ${isAIPanelOpen ? 'translate-x-0' : 'translate-x-full'}`}>
                        <div className="flex flex-col h-full">
                            <div className="sticky top-0 z-10 backdrop-blur-md bg-white/90 dark:bg-gray-800/90 border-b border-gray-200 dark:border-gray-750">
                                <div className="p-4">
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-3">
                                            <div className="w-8 h-8 rounded-lg bg-primary-500/10 flex items-center justify-center">
                                                <svg className="w-5 h-5 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                </svg>
                                            </div>
                                            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">AI Copilot</h2>
                                        </div>
                                        <button 
                                            onClick={() => setIsAIPanelOpen(!isAIPanelOpen)}
                                            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-750 rounded-lg transition-colors"
                                        >
                                            <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isAIPanelOpen ? "M13 5l7 7-7 7" : "M11 19l-7-7 7-7"} />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>                            {/* Prompt History */}
                            <div className="prompt-history flex-1 overflow-auto px-4 py-6 space-y-6">
                                {promptHistory.map(entry => (
                                    <div key={entry.id} className="group bg-gray-50 dark:bg-gray-850 p-4 rounded-xl border border-gray-100 dark:border-gray-750 hover:border-primary-200 dark:hover:border-primary-900 transition-colors">
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-2">
                                                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                {entry.timestamp}
                                            </div>
                                            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                                                <button className="p-1 hover:text-primary-500 rounded">
                                                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                    </svg>
                                                </button>
                                                <button className="p-1 hover:text-red-500 rounded">
                                                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        <div className="text-gray-800 dark:text-white mb-2 font-medium">
                                            {entry.prompt}
                                        </div>
                                        <div className="text-gray-600 dark:text-gray-300 text-sm">
                                            {entry.response}
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Prompt Input */}
                            <div className="sticky bottom-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-750 p-4">
                                <div className="relative">
                                    <textarea 
                                        placeholder="Ask AI Copilot..."
                                        className="w-full p-4 rounded-xl bg-gray-50 dark:bg-gray-850 border border-gray-200 dark:border-gray-750 text-gray-800 dark:text-white resize-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20 focus:border-primary-500 transition-all"
                                        rows="3"
                                    ></textarea>
                                    <button className="absolute right-3 bottom-3 p-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors flex items-center gap-2">
                                        <span>Send</span>
                                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </aside>                    {/* Google Sheets Import Modal */}
                    {showGoogleSheetsModal && (
                        <div className="google-sheets-import fixed inset-0 bg-gray-900/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fade-in">
                            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-6 max-w-md w-full border border-gray-200 dark:border-gray-750 animate-slide-in">
                                <div className="flex justify-between items-center mb-6">
                                    <div className="flex items-center gap-3">
                                        <div className="w-10 h-10 rounded-xl bg-primary-500/10 flex items-center justify-center">
                                            <svg className="w-6 h-6 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            Import from Google Sheets
                                        </h3>
                                    </div>
                                    <button 
                                        onClick={() => setShowGoogleSheetsModal(false)}
                                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-750 rounded-lg transition-colors"
                                    >
                                        <svg className="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                                <div className="space-y-6">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Spreadsheet URL
                                        </label>
                                        <div className="relative">
                                            <input 
                                                type="text" 
                                                className="w-full p-3 pr-10 rounded-xl border border-gray-200 dark:border-gray-750 bg-white dark:bg-gray-850 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20 focus:border-primary-500 transition-all"
                                                placeholder="Paste your Google Sheets URL here"
                                            />
                                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex justify-end gap-3">
                                        <button 
                                            onClick={() => setShowGoogleSheetsModal(false)}
                                            className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-750 rounded-lg transition-colors"
                                        >
                                            Cancel
                                        </button>
                                        <button className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors shadow-sm shadow-primary-500/20 hover:shadow-primary-500/30 flex items-center gap-2">
                                            <span>Import</span>
                                            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
