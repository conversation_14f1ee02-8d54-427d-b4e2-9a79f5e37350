<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sri Lanka Ministers Mind Map</title>
    <script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const App = () => {
          const [expandedNodes, setExpandedNodes] = React.useState({});
          
          // Data extracted from the gazette document
          const ministersData = [
            {
              id: "defence",
              name: "Minister of Defence",
              color: "#8dd3c7",
              children: [
                {
                  type: "departments",
                  name: "Departments & Statutory Institutions",
                  color: "#ffffb3",
                  items: [
                    "Sri Lanka Army", 
                    "Sri Lanka Navy", 
                    "Sri Lanka Air Force",
                    "Civil Security Department",
                    "Department of Coast Guard",
                    "Office of the Chief of Defence Staff",
                    "Defence Services Command and Staff College"
                  ]
                },
                {
                  type: "functions",
                  name: "Subjects & Functions",
                  color: "#bebada",
                  items: [
                    "Implementation of the Public Security Ordinance",
                    "Prevention of terrorism",
                    "Defence of Sri Lanka",
                    "Formulation and implementation of policies on national security"
                  ]
                },
                {
                  type: "laws",
                  name: "Laws & Acts",
                  color: "#fb8072",
                  items: [
                    "Army Act (Chapter 357)",
                    "Navy Act (Chapter 358)",
                    "Air Force Act (Chapter 359)",
                    "Public Security Ordinance (Chapter 40)",
                    "Prevention of Terrorism Act, No. 48 of 1979",
                    "Civil Security Department Act, No. 35 of 2007",
                    "Department of Coast Guard Act, No. 41 of 2009",
                    "Official Secrets Act (Chapter 42)"
                  ]
                }
              ]
            },
            // [Rest of the ministers data...]
            // Copy all the ministers data from the Code Playground
          ];

          const toggleNode = (id) => {
            setExpandedNodes(prev => ({
              ...prev,
              [id]: !prev[id]
            }));
          };

          const renderMinisterNode = (minister, index) => {
            const isExpanded = expandedNodes[minister.id];
            
            return (
              <div key={minister.id} className="minister-node">
                <div 
                  className="minister-header" 
                  style={{ backgroundColor: minister.color }}
                  onClick={() => toggleNode(minister.id)}
                >
                  <span className="expand-icon">{isExpanded ? '▼' : '►'}</span>
                  <h3>{minister.name}</h3>
                </div>
                
                {isExpanded && (
                  <div className="minister-details">
                    {minister.children.map((category, catIndex) => (
                      <div 
                        key={`${minister.id}-${category.type}`} 
                        className="category-container"
                      >
                        <div 
                          className="category-header" 
                          style={{ backgroundColor: category.color }}
                          onClick={() => toggleNode(`${minister.id}-${category.type}`)}
                        >
                          <span className="expand-icon">
                            {expandedNodes[`${minister.id}-${category.type}`] ? '▼' : '►'}
                          </span>
                          <h4>{category.name}</h4>
                        </div>
                        
                        {expandedNodes[`${minister.id}-${category.type}`] && (
                          <ul className="items-list">
                            {category.items.map((item, itemIndex) => (
                              <li key={`${minister.id}-${category.type}-${itemIndex}`}>
                                {item}
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          };

          return (
            <div className="ministers-mind-map">
              <h1>Sri Lanka Ministers Mind Map</h1>
              <p>Click on a minister or category to expand/collapse</p>
              <div className="ministers-container">
                {ministersData.map((minister, index) => renderMinisterNode(minister, index))}
              </div>
              <div className="legend">
                <h3>Color Legend</h3>
                <div className="legend-item">
                  <span className="color-box" style={{backgroundColor: "#ffffb3"}}></span>
                  <span>Departments & Statutory Institutions</span>
                </div>
                <div className="legend-item">
                  <span className="color-box" style={{backgroundColor: "#bebada"}}></span>
                  <span>Subjects & Functions</span>
                </div>
                <div className="legend-item">
                  <span className="color-box" style={{backgroundColor: "#fb8072"}}></span>
                  <span>Laws & Acts</span>
                </div>
              </div>
              <style>{`
                .ministers-mind-map {
                  font-family: Arial, sans-serif;
                  max-width: 1000px;
                  margin: 0 auto;
                  padding: 20px;
                }
                
                h1 {
                  text-align: center;
                  color: #333;
                }
                
                p {
                  text-align: center;
                  color: #666;
                  margin-bottom: 30px;
                }
                
                .ministers-container {
                  display: flex;
                  flex-direction: column;
                  gap: 15px;
                }
                
                .minister-node {
                  border: 1px solid #ddd;
                  border-radius: 8px;
                  overflow: hidden;
                  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                
                .minister-header {
                  padding: 12px 15px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  transition: background-color 0.3s;
                }
                
                .minister-header:hover {
                  filter: brightness(1.05);
                }
                
                .minister-header h3 {
                  margin: 0;
                  color: #333;
                }
                
                .minister-details {
                  padding: 15px;
                  background-color: #f9f9f9;
                }
                
                .category-container {
                  margin-bottom: 12px;
                  border: 1px solid #eee;
                  border-radius: 6px;
                  overflow: hidden;
                }
                
                .category-header {
                  padding: 8px 12px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                }
                
                .category-header:hover {
                  filter: brightness(1.05);
                }
                
                .category-header h4 {
                  margin: 0;
                  color: #333;
                }
                
                .expand-icon {
                  margin-right: 10px;
                  font-size: 12px;
                  color: #333;
                }
                
                .items-list {
                  margin: 0;
                  padding: 10px 10px 10px 30px;
                  background-color: white;
                }
                
                .items-list li {
                  margin-bottom: 5px;
                }
                
                .legend {
                  margin-top: 30px;
                  padding: 15px;
                  border: 1px solid #ddd;
                  border-radius: 8px;
                  background-color: #f9f9f9;
                }
                
                .legend h3 {
                  margin-top: 0;
                  margin-bottom: 10px;
                  color: #333;
                }
                
                .legend-item {
                  display: flex;
                  align-items: center;
                  margin-bottom: 8px;
                }
                
                .color-box {
                  display: inline-block;
                  width: 20px;
                  height: 20px;
                  margin-right: 10px;
                  border: 1px solid #ddd;
                  border-radius: 3px;
                }
              `}</style>
            </div>
          );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>