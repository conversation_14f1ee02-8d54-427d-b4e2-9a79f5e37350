<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# Chart Builder Application

This is a Next.js application with TypeScript and Tailwind CSS for building interactive charts and dashboards.

Key features and components:
1. AI Copilot Sidebar
2. Google Sheets Integration
3. Prompt History View
4. Drag-and-Drop Dashboard
5. Light/Dark Mode Support
6. Mobile Responsive Layout

Please follow these guidelines when generating code:
- Use TypeScript for type safety
- Follow Tailwind CSS best practices for styling
- Implement responsive design patterns
- Use modern React patterns (hooks, context, etc.)
- Keep accessibility in mind
- Follow clean code principles
