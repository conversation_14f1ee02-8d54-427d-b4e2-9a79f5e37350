<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sri Lanka Ministers Mind Map</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #111827;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #ffffff;
            min-height: 100vh;
            overflow-x: auto;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            color: #94a3b8;
        }

        .mind-map {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .minister-card {
            background: #1f2937;
            border-radius: 16px;
            border: 1px solid #374151;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .minister-card:hover {
            transform: translateY(-5px);
            border-color: #4f46e5;
        }

        .minister-header {
            padding: 20px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            border-radius: 15px 15px 0 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            background: var(--minister-color);
        }

        .minister-title {
            font-size: 1.4em;
            font-weight: 600;
            margin-left: 15px;
            flex-grow: 1;
            color: #fff;
        }

        .expand-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .minister-card.expanded .expand-icon {
            transform: rotate(90deg);
        }

        .view-hierarchy-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-size: 1.2em;
        }

        .view-hierarchy-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.1);
        }

        /* Minister specific colors - More modern palette */
        .minister-defence { --minister-color: #4f46e5; }
        .minister-finance { --minister-color: #0ea5e9; }
        .minister-energy { --minister-color: #f59e0b; }
        .minister-justice { --minister-color: #10b981; }
        .minister-security { --minister-color: #6366f1; }
        .minister-foreign { --minister-color: #8b5cf6; }
        .minister-labour { --minister-color: #ec4899; }

        /* Category colors - More modern palette */
        .category-departments { --category-color: #f59e0b; }
        .category-functions { --category-color: #8b5cf6; }
        .category-laws { --category-color: #ef4444; }

        .mind-map {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .minister-card {
            background: #1e293b;
            border-radius: 12px;
            border: 1px solid #334155;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .minister-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .minister-header {
            padding: 15px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            border-radius: 12px 12px 0 0;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
            background: var(--minister-color);
        }

        .minister-title {
            font-size: 1.3em;
            font-weight: 500;
            margin-left: 10px;
            flex-grow: 1;
            color: #ffffff;
        }

        .expand-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .minister-card.expanded .expand-icon {
            transform: rotate(90deg);
        }

        .view-hierarchy-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            font-size: 1.1em;
        }

        .view-hierarchy-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.1);
        }

        .category {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .category-header {
            padding: 10px 15px;
            cursor: pointer;
            position: relative;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, var(--category-color), transparent);
        }

        .category-header:hover {
            background: linear-gradient(135deg, var(--category-color), rgba(255, 255, 255, 0.1));
            transform: translateX(5px);
        }

        .category-title {
            font-size: 1.1em;
            font-weight: 500;
            margin-left: 10px;
            color: #ffffff;
        }

        .category-icon {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            margin-right: 10px;
            transition: transform 0.3s ease;
        }

        .category.expanded .category-icon {
            transform: translateY(-50%) rotate(90deg);
        }

        .category-items {
            display: none;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
        }

        .category.expanded .category-items {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .item {
            padding: 10px 15px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid var(--category-color);
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .item .website-link {
            opacity: 0;
            margin-left: 10px;
            color: #4ecdc4;
            text-decoration: none;
            font-size: 1.2em;
            transition: all 0.3s ease;
        }

        .item:hover .website-link {
            opacity: 1;
        }

        .item .website-link:hover {
            color: #45b7d1;
            transform: scale(1.1);
        }

        .legend {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .legend h3 {
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
            color: #4ecdc4;
        }

        .legend-items {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .legend-item {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .legend-item:hover {
            transform: scale(1.05);
        }

        .color-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 10px;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            width: 95%;
            height: 90%;
            margin: 2% auto;
            background: rgba(30, 30, 30, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 0 50px rgba(78, 205, 196, 0.2);
            border: 1px solid rgba(78, 205, 196, 0.3);
        }

        .close-modal {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 24px;
            color: #fff;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.1);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-modal:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .hierarchy-canvas {
            width: 100%;
            height: calc(100% - 60px);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 768px) {
            .mind-map {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .search-box {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sri Lankan Government Map</h1>
            <p>Comprehensive visualization of ministries, departments, and their functions</p>
        </div>

        <div class="search-container">
            <input type="text" class="search-box" placeholder="Search ministries, departments, or functions..." id="searchBox">
        </div>

        <div class="controls">
            <button class="btn" onclick="expandAll()">Expand All</button>
            <button class="btn" onclick="collapseAll()">Collapse All</button>
        </div>

        <div class="mind-map" id="mindMap">
            <!-- Ministers will be populated by JavaScript -->
        </div>

        <div class="legend">
            <h3>🎨 Color Legend</h3>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="color-indicator" style="background: #ffcc80;"></div>
                    <span>Departments & Institutions</span>
                </div>
                <div class="legend-item">
                    <div class="color-indicator" style="background: #b39ddb;"></div>
                    <span>Subjects & Functions</span>
                </div>
                <div class="legend-item">
                    <div class="color-indicator" style="background: #ef5350;"></div>
                    <span>Laws & Acts</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const ministersData = [
            {
                id: "defence",
                name: "Minister of Defence",
                class: "minister-defence",
                departments: [
                    { name: "Sri Lanka Army", url: "https://www.army.lk/" },
                    { name: "Sri Lanka Navy", url: "https://www.navy.lk/" },
                    { name: "Sri Lanka Air Force", url: "http://www.airforce.lk/" },
                    { name: "Civil Security Department", url: "http://www.csd.lk/" },
                    { name: "Department of Coast Guard", url: "http://www.coastguard.gov.lk/" },
                    { name: "Sir John Kotelawala Defence University", url: "https://www.kdu.ac.lk/" },
                    { name: "Department of Meteorology", url: "http://www.meteo.gov.lk/" },
                    { name: "Disaster Management Centre", url: "http://www.dmc.gov.lk/" },
                    { name: "National Building Research Organization", url: "https://www.nbro.gov.lk/" }
                ],
                functions: [
                    "Formulation, implementation, monitoring and evaluation of policies, strategies, programmes, and projects in relation to defence and disaster management",
                    "Provision of public services efficiently and in a people-friendly manner",
                    "Reforming systems using modern management techniques to eliminate corruption",
                    "Ensuring national security", "Maintenance of internal security",
                    "Upgrading integrated intelligence mechanism", "Establishment of National Security Advisory Board",
                    "Maintenance of relations with visiting Armed Forces", "Extension of cooperation to international humanitarian operations",
                    "Responsibilities related to explosives, firearms, offensive weapons, and chemical weapons",
                    "Maintenance of lighthouses", "Provision of defence education and higher education for defence personnel",
                    "Matters relating to private security services", "Enhancing excellence in defence with science, technology, and innovation",
                    "Technical updating of Navy and Coast Guard for EEZ security", "Rescue operations and administration of Coast Guard Service",
                    "Matters relating to extradition", "Matters relating to veteran and disabled soldiers",
                    "Provision of weather and climate services", "Landslide disaster management and research"
                ],
                laws: [
                    "Chief of Defence Staff Act No. 35 of 2009", "Army Act No. 17 of 1949", "Navy Act No. 34 of 1950",
                    "Air Force Act No. 41 of 1949", "Extradition Law No. 8 of 1977", "Explosives Act No. 21 of 1956",
                    "Firearms Ordinance No. 33 of 1916", "Firing Ranges and Military Training Act No. 24 of 1951",
                    "Mobilization and Supplementary Forces Act No. 40 of 1985", "Offensive Weapons Act, No. 18 of 1966",
                    "Piracy Act No. 9 of 2001", "Prevention of Terrorism Act No. 48 of 1979",
                    "Public Security Ordinance No. 25 of 1947", "Chemical Weapons Convention Act No. 58 of 2007"
                ]
            },
            {
                id: "finance",
                name: "Minister of Finance, Planning and Economic Development",
                class: "minister-finance",
                departments: [
                    { name: "Department of Inland Revenue", url: "http://www.ird.gov.lk/" },
                    { name: "Sri Lanka Customs", url: "http://www.customs.gov.lk/" },
                    { name: "Central Bank of Sri Lanka", url: "https://www.cbsl.gov.lk/" },
                    { name: "Department of Census and Statistics", url: "http://www.statistics.gov.lk/" },
                    { name: "Department of External Resources", url: "http://www.erd.gov.lk/" },
                    { name: "National Lotteries Board", url: "https://www.nlb.lk/" },
                    { name: "Securities and Exchange Commission", url: "https://www.sec.gov.lk/" },
                    { name: "Insurance Regulatory Commission", url: "https://www.ircsl.gov.lk/" },
                    { name: "Department of Public Finance", url: "http://www.treasury.gov.lk/" },
                    { name: "Department of Trade and Investment Policy", url: "http://www.tipd.lk/" },
                    { name: "Sri Lanka Export Development Board", url: "http://www.edb.gov.lk/" }
                ],
                functions: [
                    "Formulation of national economic and financial policies and strategies",
                    "Preparation of National Development and Public Investment Programmes",
                    "Formulation of fiscal and macro-fiscal management policies",
                    "Implementation of policy measures for sustainable economic growth",
                    "Execution of measures to ensure debt sustainability",
                    "Maintaining transparency and accountability in public financial management",
                    "Coordination of cross-sectoral national policy priorities",
                    "Facilitation of international and regional trade integration",
                    "Preparation of the annual budget and management of financial resources"
                ],
                laws: [
                    "Finance Business Act No. 42 of 2011", "Foreign Exchange Act No. 12 of 2017",
                    "Census Ordinance (Chapter 143)", "Insurance Corporation Act No. 02 of 1961",
                    "Credit Information Bureau of Sri Lanka Act No. 18 of 1990", "Microfinance Act No. 6 of 2016",
                    "Finance Companies Act No. 78 of 1988", "Exchange Control Act No. 24 of 1953",
                    "Sri Lanka State Mortgage and Investment Bank Act No. 13 of 1975", "Casino Business (Regulation) Act No. 17 of 2010"
                ]
            },
            {
                id: "energy",
                name: "Minister of Energy",
                class: "minister-energy",
                departments: [
                    { name: "Ceylon Electricity Board", url: "https://ceb.lk/" },
                    { name: "Ceylon Petroleum Corporation", url: "https://ceypetco.gov.lk/" },
                    { name: "Sri Lanka Sustainable Energy Authority", url: "http://www.energy.gov.lk/" },
                    { name: "Sri Lanka Atomic Energy Board", url: "https://www.aeb.gov.lk/" },
                    { name: "Lanka Coal Company", url: "http://www.lankacoal.lk/" },
                    { name: "LTL Holdings", url: "https://www.ltl.lk/" },
                    { name: "Sri Lanka Atomic Energy Regulatory Council", url: "https://www.aerc.gov.lk/" }
                ],
                functions: [
                    "Formulation of policies related to energy sector",
                    "Exploration, planning, and development of renewable energy",
                    "Meeting electricity needs and ensuring energy security",
                    "Management of demand for energy efficiency", "Implementation of long-term power generation plans",
                    "Making power transmission and distribution efficient", "Creating a smart network for electricity use"
                ],
                laws: [
                    "Ceylon Electricity Board Act No. 17 of 1969", "Sri Lanka Electricity Act No. 20 of 2009",
                    "Sri Lanka Electricity Act No. 36 of 2024", "Ceylon Petroleum Corporation Act No. 28 of 1961",
                    "Petroleum Resources Act No. 21 of 2021", "Sri Lanka Sustainable Energy Authority Act No. 35 of 2007"
                ]
            },
            {
                id: "justice",
                name: "Minister of Justice and National Integration",
                class: "minister-justice",
                departments: [
                    { name: "Attorney General's Department", url: "https://www.attorneygeneral.gov.lk/" },
                    { name: "Legal Draftsman's Department", url: "https://www.lawdept.gov.lk/" },
                    { name: "Department of Prisons", url: "http://www.prisons.gov.lk/" },
                    { name: "Law Commission of Sri Lanka", url: "https://lawcom.gov.lk/" },
                    { name: "Legal Aid Commission of Sri Lanka", url: "https://www.legalaid.gov.lk/" },
                    { name: "Department of Official Languages", url: "https://www.languagesdept.gov.lk/" },
                    { name: "Office for National Unity and Reconciliation", url: "https://onur.gov.lk/" },
                    { name: "Supreme Court of Sri Lanka", url: "https://www.supremecourt.lk/" },
                    { name: "Council of Legal Education", url: "http://www.sllc.gov.lk/" }
                ],
                functions: [
                    "Formulation of policies related to justice and national integration",
                    "Reforming the legal system", "Consolidation of laws", "Administration of courts of justice",
                    "Preventing delays in the court system", "Criminal prosecutions and civil proceedings for the government"
                ],
                laws: [
                    "Superior Courts Complex Board of Management Act No. 50 of 1987", "Judicature Act No. 2 of 1978",
                    "Law Commission Act No. 3 of 1969", "Legal Aid Law No. 11 of 1978", "Mediation Boards Act No. 72 of 1988"
                ]
            },
            {
                id: "security",
                name: "Minister of Public Security and Parliamentary Affairs",
                class: "minister-security",
                departments: [
                    { name: "Sri Lanka Police", url: "https://www.police.lk/" },
                    { name: "Department of Immigration and Emigration", url: "https://www.immigration.gov.lk/" },
                    { name: "Department of Registration of Persons", url: "https://www.drp.gov.lk/" },
                    { name: "National Dangerous Drugs Control Board", url: "http://www.nddcb.gov.lk/" }
                ],
                functions: [
                    "Maintenance of law and order", "Implementation of strategies for community discipline",
                    "Prevention and combating of crimes", "Controlling vehicular traffic", "Implementing reforms to police services"
                ],
                laws: [
                    "Police Ordinance", "Immigrants and Emigrants Act", "Registration of Persons Act",
                    "National Dangerous Drugs Control Board Act No. 11 of 1984"
                ]
            },
            {
                id: "foreign",
                name: "Minister of Foreign Affairs, Foreign Employment and Tourism",
                class: "minister-foreign",
                departments: [
                    { name: "Ministry of Foreign Affairs", url: "https://www.mfa.gov.lk/" },
                    { name: "Sri Lanka Foreign Employment Bureau", url: "http://www.slbfe.lk/" },
                    { name: "Sri Lanka Tourism Promotion Bureau", url: "https://srilanka.travel/" },
                    { name: "Sri Lanka Tourism Development Authority", url: "https://www.sltda.gov.lk/" },
                    { name: "Department of Immigration and Emigration", url: "https://www.immigration.gov.lk/" },
                    { name: "Lakshman Kadirgamar Institute", url: "https://www.lki.lk/" },
                    { name: "Foreign Employment Agency of Sri Lanka", url: "http://www.felanka.lk/" }
                ],
                functions: [
                    "Foreign relations and diplomatic affairs", "Protection of Sri Lanka's interests abroad",
                    "Promotion of international cooperation", "Consular functions"
                ],
                laws: [
                    "Diplomatic Privileges Act", "Consular Functions Act",
                    "Lakshman Kadirgamar Institute for International Relations and Strategic Studies Act"
                ]
            },
            {
                id: "labour",
                name: "Minister of Labour",
                class: "minister-labour",
                departments: [
                    { name: "Department of Labour", url: "http://www.labourdept.gov.lk/" },
                    { name: "National Institute of Occupational Safety and Health", url: "http://www.niosh.gov.lk/" },
                    { name: "Employees' Trust Fund Board", url: "http://www.etfb.gov.lk/" },
                    { name: "Employees' Provident Fund", url: "http://www.epf.gov.lk/" },
                    { name: "Office of the Commissioner of Workmen's Compensation", url: "http://www.labourcomp.gov.lk/" },
                    { name: "Department of Manpower and Employment", url: "http://www.dome.gov.lk/" }
                ],
                functions: [
                    "Formulation of policies for labour relations", "Employee administration, welfare, and health",
                    "Compliance with international labour standards", "Administration of labour tribunals"
                ],
                laws: [
                    "Employees' Councils Act No. 32 of 1979", "Employees' Provident Fund Act No. 15 of 1958",
                    "Employment of Women, Young Persons, and Children Act No. 47 of 1956", "Factories Ordinance No. 45 of 1942"
                ]
            }
        ];

        const ministersHierarchy = {
            defence: {
                name: "Minister of Defence",
                children: [
                    {
                        name: "Secretary of Defence",
                        children: [
                            {
                                name: "Chief of Defence Staff",
                                children: [
                                    { name: "Army Commander" },
                                    { name: "Navy Commander" },
                                    { name: "Air Force Commander" }
                                ]
                            },
                            {
                                name: "Additional Secretary (Defence)",
                                children: [
                                    { name: "Civil Security Department" },
                                    { name: "Coast Guard Department" }
                                ]
                            },
                            {
                                name: "Additional Secretary (Policy)",
                                children: [
                                    { name: "Defence Research Unit" },
                                    { name: "Policy Planning Division" }
                                ]
                            }
                        ]
                    }
                ]
            },
            // Add hierarchies for other ministries similarly
        };

        function createMinisterCard(minister) {
            return `
                <div class="minister-card ${minister.class}" id="${minister.id}">
                    <div class="minister-header" onclick="toggleMinister('${minister.id}')">
                        <div class="expand-icon">▶</div>
                        <div class="minister-title">${minister.name}</div>
                        <button class="view-hierarchy-btn" onclick="event.stopPropagation(); showHierarchy('${minister.id}')" title="View Organization Chart">
                            📊
                        </button>
                    </div>
                    <div class="minister-content">
                        <div class="category category-departments">
                            <div class="category-header" onclick="toggleCategory('${minister.id}-departments')">
                                <div class="category-icon">▶</div>
                                <div class="category-title">🏛️ Departments & Statutory Institutions</div>
                            </div>
                            <div class="category-items">
                                ${minister.departments.map(dept => `
                                    <div class="item">
                                        <span>${dept.name || dept}</span>
                                        ${dept.url ? `<a href="${dept.url}" target="_blank" class="website-link" title="Visit website">🔗</a>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="category category-functions">
                            <div class="category-header" onclick="toggleCategory('${minister.id}-functions')">
                                <div class="category-icon">▶</div>
                                <div class="category-title">⚙️ Subjects & Functions</div>
                            </div>
                            <div class="category-items">
                                ${minister.functions.map(func => `<div class="item">${func}</div>`).join('')}
                            </div>
                        </div>
                        <div class="category category-laws">
                            <div class="category-header" onclick="toggleCategory('${minister.id}-laws')">
                                <div class="category-icon">▶</div>
                                <div class="category-title">📜 Laws & Acts</div>
                            </div>
                            <div class="category-items">
                                ${minister.laws.map(law => `<div class="item">${law}</div>`).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function toggleMinister(ministerId) {
            const card = document.getElementById(ministerId);
            const wasExpanded = card.classList.contains('expanded');
            
            // First collapse all cards
            document.querySelectorAll('.minister-card').forEach(c => {
                c.classList.remove('expanded');
                c.querySelectorAll('.category').forEach(cat => cat.classList.remove('expanded'));
            });
            
            // Then expand the clicked card if it wasn't expanded before
            if (!wasExpanded) {
                card.classList.add('expanded');
            }
        }

        function toggleCategory(categoryId) {
            const category = document.querySelector(`#${categoryId.split('-')[0]} .category-${categoryId.split('-')[1]}`);
            const wasExpanded = category.classList.contains('expanded');
            
            // Collapse all categories in this card
            const card = document.getElementById(categoryId.split('-')[0]);
            card.querySelectorAll('.category').forEach(c => c.classList.remove('expanded'));
            
            // Then expand the clicked category if it wasn't expanded before
            if (!wasExpanded) {
                category.classList.add('expanded');
            }
        }

        function expandAll() {
            document.querySelectorAll('.minister-card').forEach(card => {
                card.classList.add('expanded');
            });
            document.querySelectorAll('.category').forEach(category => {
                category.classList.add('expanded');
            });
        }

        function collapseAll() {
            document.querySelectorAll('.minister-card').forEach(card => {
                card.classList.remove('expanded');
            });
            document.querySelectorAll('.category').forEach(category => {
                category.classList.remove('expanded');
            });
        }

        function initializeSearch() {
            const searchBox = document.getElementById('searchBox');
            searchBox.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const cards = document.querySelectorAll('.minister-card');
                
                cards.forEach(card => {
                    const cardText = card.textContent.toLowerCase();
                    if (cardText.includes(searchTerm)) {
                        card.style.display = 'block';
                        // Highlight matching items
                        const items = card.querySelectorAll('.item');
                        items.forEach(item => {
                            if (item.textContent.toLowerCase().includes(searchTerm)) {
                                item.classList.add('highlight');
                            } else {
                                item.classList.remove('highlight');
                            }
                        });
                    } else {
                        card.style.display = searchTerm === '' ? 'block' : 'none';
                    }
                });
            });
        }

        // Initialize the mind map
        function init() {
            const mindMapContainer = document.getElementById('mindMap');
            mindMapContainer.innerHTML = ministersData.map(minister => createMinisterCard(minister)).join('');
            initializeSearch();
        }

        // Run initialization when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);

        // Add the modal HTML
        document.body.insertAdjacentHTML('beforeend', `
            <div id="hierarchyModal" class="modal">
                <div class="modal-content">
                    <span class="close-modal" onclick="closeHierarchyModal()">×</span>
                    <canvas id="hierarchyCanvas" class="hierarchy-canvas"></canvas>
                </div>
            </div>
        `);

        function showHierarchy(ministryId) {
            const modal = document.getElementById('hierarchyModal');
            const canvas = document.getElementById('hierarchyCanvas');
            const ctx = canvas.getContext('2d');
            modal.style.display = 'block';
            
            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw hierarchy
            drawHierarchy(ctx, ministersHierarchy[ministryId], canvas.width / 2, 50, canvas.width * 0.8);
        }

        function closeHierarchyModal() {
            document.getElementById('hierarchyModal').style.display = 'none';
        }

        function drawHierarchy(ctx, node, x, y, width, level = 0) {
            const BOX_HEIGHT = 40;
            const VERTICAL_SPACING = 80;
            const boxWidth = Math.min(width * 0.8, 200);
            
            // Draw current node
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 2;
            
            // Box
            roundRect(ctx, x - boxWidth/2, y, boxWidth, BOX_HEIGHT, 10);
            
            // Text
            ctx.fillStyle = '#ffffff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(node.name, x, y + BOX_HEIGHT/2);
            
            // Draw children
            if (node.children && node.children.length > 0) {
                const childrenWidth = width / node.children.length;
                node.children.forEach((child, index) => {
                    const childX = x - (width/2) + (childrenWidth * (index + 0.5));
                    const childY = y + VERTICAL_SPACING;
                    
                    // Draw connection line
                    ctx.beginPath();
                    ctx.moveTo(x, y + BOX_HEIGHT);
                    ctx.lineTo(childX, childY);
                    ctx.stroke();
                    
                    // Draw child hierarchy
                    drawHierarchy(ctx, child, childX, childY, childrenWidth, level + 1);
                });
            }
        }

        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
        }

        // Handle window resize to adjust canvas size
        window.addEventListener('resize', () => {
            const modal = document.getElementById('hierarchyModal');
            if (modal.style.display === 'block') {
                const canvas = document.getElementById('hierarchyCanvas');
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
                // Redraw the current hierarchy
                const activeMinistry = Object.keys(ministersHierarchy).find(id => 
                    document.getElementById(id).classList.contains('active-hierarchy')
                );
                if (activeMinistry) {
                    drawHierarchy(canvas.getContext('2d'), ministersHierarchy[activeMinistry], 
                        canvas.width / 2, 50, canvas.width * 0.8);
                }
            }
        });
    </script>
</body>
</html>